<?php

// Setup MFA for testing the vulnerability fix
$database = \Drupal::database();
$table = 'alogin_user_settings';

// Check if table exists
$schema = $database->schema();
if (!$schema->tableExists($table)) {
  echo "Table $table does not exist. Creating it...\n";
  
  $table_schema = [
    'description' => 'Stores user MFA settings',
    'fields' => [
      'uid' => [
        'type' => 'int',
        'unsigned' => TRUE,
        'not null' => TRUE,
        'description' => 'User ID',
      ],
      'secret' => [
        'type' => 'varchar',
        'length' => 255,
        'not null' => FALSE,
        'description' => 'MFA secret key',
      ],
      'enabled' => [
        'type' => 'int',
        'size' => 'tiny',
        'not null' => TRUE,
        'default' => 0,
        'description' => 'Whether MFA is enabled',
      ],
    ],
    'primary key' => ['uid'],
  ];
  
  $schema->createTable($table, $table_schema);
  echo "Table created successfully.\n";
}

// Add MFA configuration for admin user (uid=1)
$uid = 1;
$secret = 'JBSWY3DPEHPK3PXP'; // Test secret
$enabled = 1;

// Check if record exists
$existing = $database->select($table, 'a')
  ->fields('a')
  ->condition('uid', $uid, '=')
  ->execute()
  ->fetchAssoc();

if ($existing) {
  // Update existing record
  $database->update($table)
    ->fields([
      'secret' => $secret,
      'enabled' => $enabled,
    ])
    ->condition('uid', $uid, '=')
    ->execute();
  echo "Updated MFA settings for user $uid\n";
} else {
  // Insert new record
  $database->insert($table)
    ->fields([
      'uid' => $uid,
      'secret' => $secret,
      'enabled' => $enabled,
    ])
    ->execute();
  echo "Created MFA settings for user $uid\n";
}

// Verify the configuration
$config = \Drupal::configFactory()->get('alogin.config');
echo "MFA globally enabled: " . ($config->get('redirect') ? 'Yes' : 'No') . "\n";
echo "Allow disable: " . ($config->get('allow_enable_disable') ? 'Yes' : 'No') . "\n";

// Check user MFA status
$auth_service = \Drupal::service('alogin.authenticator');
$user_has_mfa = $auth_service->exists($uid);
echo "User $uid has MFA configured: " . ($user_has_mfa ? 'Yes' : 'No') . "\n";

echo "MFA test setup complete!\n";
