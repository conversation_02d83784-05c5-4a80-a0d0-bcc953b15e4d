name: Tests
on: pull_request
jobs:
  phpunit:
    name: PHP ${{ matrix.php }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        php:
          - "5.3"
          - "5.4"
          - "5.5"
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Install xvfb
        run: |
          sudo apt-get update
          sudo apt-get install xvfb

      - name: Install wkhtmltopdf
        run: |
          cd /tmp
          sudo wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6-1/wkhtmltox_0.12.6-1.focal_amd64.deb
          sudo apt install -y -q ./wkhtmltox_0.12.6-1.focal_amd64.deb
          wkhtmltopdf --version

      - name: Install PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2

      - name: Update composer
        run: composer self-update

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache composer cache
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install composer packages
        run: composer update --prefer-dist --no-interaction --no-progress --optimize-autoloader --ansi

      - name: Run phpunit
        run: vendor/bin/phpunit --color=always
