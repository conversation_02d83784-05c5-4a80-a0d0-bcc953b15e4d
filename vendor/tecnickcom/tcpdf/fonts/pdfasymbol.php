<?php
// TCPDF FONT FILE DESCRIPTION
$type='Type1';
$name='PDFASymbol';
$up=-100;
$ut=50;
$dw=500;
$diff='32 /space /exclam /universal /numbersign /existential /percent /ampersand /suchthat /parenleft /parenright /asteriskmath /plus /comma /minus /period /slash /zero /one /two /three /four /five /six /seven /eight /nine /colon /semicolon /less /equal /greater /question /congruent /Alpha /Beta /Chi /Delta /Epsilon /Phi /Gamma /Eta /Iota /theta1 /Kappa /Lambda /Mu /Nu /Omicron /Pi /Theta /Rho /Sigma /Tau /Upsilon /sigma1 /Omega /Xi /Psi /Zeta /bracketleft /therefore /bracketright /perpendicular /underscore /radicalex /alpha /beta /chi /delta /epsilon /phi /gamma /eta /iota /phi1 /kappa /lambda /mu /nu /omicron /pi /theta /rho /sigma /tau /upsilon /omega1 /omega /xi /psi /zeta /braceleft /bar /braceright /similar /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /.notdef /Euro /Upsilon1 /minute /lessequal /fraction /infinity /florin /club /diamond /heart /spade /arrowboth /arrowleft /arrowup /arrowright /arrowdown /degree /plusminus /second /greaterequal /multiply /proportional /partialdiff /bullet /divide /notequal /equivalence /approxequal /ellipsis /arrowvertex /arrowhorizex /carriagereturn /aleph /Ifraktur /Rfraktur /weierstrass /circlemultiply /circleplus /emptyset /intersection /union /propersuperset /reflexsuperset /notsubset /propersubset /reflexsubset /element /notelement /angle /gradient /registerserif /copyrightserif /trademarkserif /product /radical /dotmath /logicalnot /logicaland /logicalor /arrowdblboth /arrowdblleft /arrowdblup /arrowdblright /arrowdbldown /lozenge /angleleft /registersans /copyrightsans /trademarksans /summation /parenlefttp /parenleftex /parenleftbt /bracketlefttp /bracketleftex /bracketleftbt /bracelefttp /braceleftmid /braceleftbt /braceex /.notdef /angleright /integral /integraltp /integralex /integralbt /parenrighttp /parenrightex /parenrightbt /bracketrighttp /bracketrightex /bracketrightbt /bracerighttp /bracerightmid /bracerightbt /.notdef ';
$enc='symbol';
$file='pdfasymbol.z';
$size1=4188;
$size2=28326;
$desc=array('Flags'=>4,'FontBBox'=>'[-168 -233 997 900]','ItalicAngle'=>0,'Ascent'=>900,'Descent'=>-233,'Leading'=>0,'CapHeight'=>661,'XHeight'=>460,'StemV'=>70,'StemH'=>30,'AvgWidth'=>578,'MaxWidth'=>1008,'MissingWidth'=>500);
$cw=array(0=>500,1=>500,2=>500,3=>500,4=>500,5=>500,6=>500,7=>500,8=>500,9=>500,10=>500,11=>500,12=>500,13=>500,14=>500,15=>500,16=>500,17=>500,18=>500,19=>500,20=>500,21=>500,22=>500,23=>500,24=>500,25=>500,26=>500,27=>500,28=>500,29=>500,30=>500,31=>500,32=>250,33=>333,34=>587,35=>500,36=>587,37=>833,38=>778,39=>439,40=>333,41=>333,42=>471,43=>564,44=>250,45=>564,46=>250,47=>278,48=>500,49=>500,50=>500,51=>500,52=>500,53=>500,54=>500,55=>500,56=>500,57=>500,58=>278,59=>278,60=>564,61=>564,62=>564,63=>444,64=>636,65=>722,66=>667,67=>722,68=>660,69=>611,70=>742,71=>586,72=>722,73=>333,74=>587,75=>722,76=>722,77=>889,78=>722,79=>722,80=>722,81=>726,82=>556,83=>627,84=>611,85=>696,86=>452,87=>743,88=>628,89=>808,90=>611,91=>333,92=>565,93=>333,94=>712,95=>500,96=>500,97=>583,98=>527,99=>480,100=>534,101=>426,102=>643,103=>480,104=>536,105=>286,106=>640,107=>544,108=>476,109=>536,110=>480,111=>500,112=>587,113=>534,114=>528,115=>537,116=>420,117=>514,118=>684,119=>693,120=>514,121=>693,122=>466,123=>480,124=>200,125=>480,126=>636,127=>500,128=>500,129=>500,130=>500,131=>500,132=>500,133=>500,134=>500,135=>500,136=>500,137=>500,138=>500,139=>500,140=>500,141=>500,142=>500,143=>500,144=>500,145=>500,146=>500,147=>500,148=>500,149=>500,150=>500,151=>500,152=>500,153=>500,154=>500,155=>500,156=>500,157=>500,158=>500,159=>500,160=>500,161=>620,162=>247,163=>636,164=>167,165=>853,166=>358,167=>770,168=>770,169=>770,170=>770,171=>964,172=>964,173=>472,174=>964,175=>500,176=>400,177=>564,178=>411,179=>636,180=>564,181=>636,182=>494,183=>350,184=>564,185=>564,186=>636,187=>636,188=>1000,189=>500,190=>500,191=>658,192=>537,193=>613,194=>711,195=>832,196=>636,197=>636,198=>746,199=>654,200=>654,201=>636,202=>636,203=>636,204=>636,205=>636,206=>536,207=>536,208=>575,209=>612,210=>792,211=>793,212=>1008,213=>823,214=>549,215=>250,216=>564,217=>564,218=>564,219=>964,220=>964,221=>550,222=>964,223=>550,224=>512,225=>329,226=>500,227=>500,228=>500,229=>713,230=>500,231=>500,232=>500,233=>500,234=>500,235=>500,236=>500,237=>500,238=>500,239=>500,240=>500,241=>329,242=>416,243=>686,244=>500,245=>686,246=>500,247=>500,248=>500,249=>500,250=>500,251=>500,252=>500,253=>500,254=>500,255=>333);
// --- EOF ---
