<?php return array(
    'root' => array(
        'name' => 'drupal/recommended-project',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        'asm89/stack-cors' => array(
            'pretty_version' => 'v2.2.0',
            'version' => '2.2.0.0',
            'reference' => '50f57105bad3d97a43ec4a485eb57daf347eafea',
            'type' => 'library',
            'install_path' => __DIR__ . '/../asm89/stack-cors',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'chi-teck/drupal-code-generator' => array(
            'pretty_version' => '3.6.1',
            'version' => '3.6.1.0',
            'reference' => '2dbd8d231945681a398862a3282ade3cf0ea23ab',
            'type' => 'library',
            'install_path' => __DIR__ . '/../chi-teck/drupal-code-generator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/installers' => array(
            'pretty_version' => 'v2.3.0',
            'version' => '2.3.0.0',
            'reference' => '12fb2dfe5e16183de69e784a7b84046c43d97e8e',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/./installers',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/semver' => array(
            'pretty_version' => '3.4.3',
            'version' => '3.4.3.0',
            'reference' => '4313d26ada5e0c4edfbd1dc481a92ff7bff91f12',
            'type' => 'library',
            'install_path' => __DIR__ . '/./semver',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/annotated-command' => array(
            'pretty_version' => '4.10.1',
            'version' => '4.10.1.0',
            'reference' => '362310b13ececa9f6f0a4a880811fa08fecc348b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/annotated-command',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/config' => array(
            'pretty_version' => '2.1.2',
            'version' => '2.1.2.0',
            'reference' => '597f8d7fbeef801736250ec10c3e190569b1b0ae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/config',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/filter-via-dot-access-data' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'cb2eeba41f8e2e3c61698a5cf70ef048ff6c9d5b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/filter-via-dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/log' => array(
            'pretty_version' => '3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'c27a3beb36137c141ccbce0d89f64befb243c015',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/output-formatters' => array(
            'pretty_version' => '4.6.0',
            'version' => '4.6.0.0',
            'reference' => '5fd5656718d7068a02d046f418a7ba873d5abbfe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/output-formatters',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/robo' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => '55a272370940607649e5c46eb173c5c54f7c166d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/robo',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/self-update' => array(
            'pretty_version' => '2.2.0',
            'version' => '2.2.0.0',
            'reference' => '972a1016761c9b63314e040836a12795dff6953a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/self-update',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/site-alias' => array(
            'pretty_version' => '4.1.1',
            'version' => '4.1.1.0',
            'reference' => 'aff6189aae17da813d23249cb2fc0fff33f26d40',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/site-alias',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'consolidation/site-process' => array(
            'pretty_version' => '5.4.2',
            'version' => '5.4.2.0',
            'reference' => 'e7fafc40ebfddc1a5ee99ee66e5d186fc1bed4da',
            'type' => 'library',
            'install_path' => __DIR__ . '/../consolidation/site-process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dflydev/dot-access-data' => array(
            'pretty_version' => 'v3.0.3',
            'version' => '3.0.3.0',
            'reference' => 'a23a2bf4f31d3518f3ecb38660c95715dfead60f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dflydev/dot-access-data',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/annotations' => array(
            'pretty_version' => '1.14.4',
            'version' => '1.14.4.0',
            'reference' => '253dca476f70808a5aeed3a47cc2cc88c5cab915',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/annotations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/deprecations' => array(
            'pretty_version' => '1.1.5',
            'version' => '1.1.5.0',
            'reference' => '459c2f5dd3d6a4633d3b5f46ee2b1c40f57d3f38',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/deprecations',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'doctrine/lexer' => array(
            'pretty_version' => '2.1.1',
            'version' => '2.1.1.0',
            'reference' => '861c870e8b75f7c8f69c146c7f89cc1c0f1b49b6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../doctrine/lexer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/dompdf' => array(
            'pretty_version' => 'v3.1.0',
            'version' => '3.1.0.0',
            'reference' => 'a51bd7a063a65499446919286fb18b518177155a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/dompdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/php-font-lib' => array(
            'pretty_version' => '1.0.1',
            'version' => '1.0.1.0',
            'reference' => '6137b7d4232b7f16c882c75e4ca3991dbcf6fe2d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-font-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dompdf/php-svg-lib' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'eb045e518185298eb6ff8d80d0d0c6b17aecd9af',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dompdf/php-svg-lib',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/admin_toolbar' => array(
            'pretty_version' => '3.6.0',
            'version' => '3.6.0.0',
            'reference' => '3.6.0',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/admin_toolbar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => '547fa74348dda2ecb4a3e752f88a5c40be675d64',
            'type' => 'drupal-core',
            'install_path' => __DIR__ . '/../../web/core',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-annotation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-assertion' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-class-finder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-composer-scaffold' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => 'db17b59620ce1c142a34dc017d9e696ce4771e55',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-composer-scaffold',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-datetime' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-dependency-injection' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-diff' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-discovery' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-event-dispatcher' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-file-cache' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-file-security' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-filesystem' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-front-matter' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-gettext' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-graph' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-http-foundation' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-php-storage' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-plugin' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-project-message' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => 'd1da83722735cb0f7ccabf9fef7b5607b442c3a8',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../drupal/core-project-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-proxy-builder' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-recommended' => array(
            'pretty_version' => '10.4.7',
            'version' => '10.4.7.0',
            'reference' => '308b63fa05111c15f4a36919718b7c2d016af892',
            'type' => 'metapackage',
            'install_path' => null,
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/core-render' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-serialization' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-transliteration' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-utility' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-uuid' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/core-version' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '10.4.7',
            ),
        ),
        'drupal/entity_print' => array(
            'pretty_version' => '2.16.0',
            'version' => '2.16.0.0',
            'reference' => '8.x-2.16',
            'type' => 'drupal-module',
            'install_path' => __DIR__ . '/../../web/modules/contrib/entity_print',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/mahi' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '1.1.0',
            'type' => 'drupal-theme',
            'install_path' => __DIR__ . '/../../web/themes/contrib/mahi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drupal/recommended-project' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'drush/drush' => array(
            'pretty_version' => '12.5.3',
            'version' => '12.5.3.0',
            'reference' => '7fe0a492d5126c457c5fb184c4668a132b0aaac6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../drush/drush',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'egulias/email-validator' => array(
            'pretty_version' => '4.0.4',
            'version' => '4.0.4.0',
            'reference' => 'd42c8731f0624ad6bdc8d3e5e9a4524f68801cfa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../egulias/email-validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grasmash/expander' => array(
            'pretty_version' => '3.0.1',
            'version' => '3.0.1.0',
            'reference' => 'eea11b9afb0c32483b18b9009f4ca07b770e39f4',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grasmash/expander',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'grasmash/yaml-cli' => array(
            'pretty_version' => '3.2.1',
            'version' => '3.2.1.0',
            'reference' => '09a8860566958a1576cc54bbe910a03477e54971',
            'type' => 'library',
            'install_path' => __DIR__ . '/../grasmash/yaml-cli',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.3',
            'version' => '7.9.3.0',
            'reference' => '7b2f29fe81dc4da0ca0ea7d42107a0845946ea77',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.4',
            'version' => '2.0.4.0',
            'reference' => 'f9c436286ab2892c7db7be8c8da4ef61ccf7b455',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.1',
            'version' => '2.7.1.0',
            'reference' => 'c2270caaabe631b3b44c85f99e5a04bbb8060d16',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/container' => array(
            'pretty_version' => '4.2.5',
            'version' => '4.2.5.0',
            'reference' => 'd3cebb0ff4685ff61c749e54b27db49319e2ec00',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'masterminds/html5' => array(
            'pretty_version' => '2.9.0',
            'version' => '2.9.0.0',
            'reference' => 'f5ac2c0b0a2eefca70b2ce32a5809992227e75a6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../masterminds/html5',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mck89/peast' => array(
            'pretty_version' => 'v1.16.3',
            'version' => '1.16.3.0',
            'reference' => '645ec21b650bc2aced18285c85f220d22afc1430',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mck89/peast',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikehaertl/php-shellcommand' => array(
            'pretty_version' => '1.7.0',
            'version' => '1.7.0.0',
            'reference' => 'e79ea528be155ffdec6f3bf1a4a46307bb49e545',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikehaertl/php-shellcommand',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikehaertl/php-tmpfile' => array(
            'pretty_version' => '1.3.0',
            'version' => '1.3.0.0',
            'reference' => 'a5392bed91f67e2849a7cb24075d346468e1b1a8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikehaertl/php-tmpfile',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mikehaertl/phpwkhtmltopdf' => array(
            'pretty_version' => '2.5.0',
            'version' => '2.5.0.0',
            'reference' => '17ee71341591415d942774eda2c98d8ba7ea9e90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mikehaertl/phpwkhtmltopdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/mpdf' => array(
            'pretty_version' => 'v8.2.5',
            'version' => '8.2.5.0',
            'reference' => 'e175b05e3e00977b85feb96a8cccb174ac63621f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/mpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-http-message-shim' => array(
            'pretty_version' => 'v2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'f25a0153d645e234f9db42e5433b16d9b113920f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-http-message-shim',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'mpdf/psr-log-aware-trait' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'a633da6065e946cc491e1c962850344bb0bf3e78',
            'type' => 'library',
            'install_path' => __DIR__ . '/../mpdf/psr-log-aware-trait',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/deep-copy' => array(
            'pretty_version' => '1.13.1',
            'version' => '1.13.1.0',
            'reference' => '1720ddd719e16cf0db4eb1c6eca108031636d46c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/deep-copy',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'nikic/php-parser' => array(
            'pretty_version' => 'v5.4.0',
            'version' => '5.4.0.0',
            'reference' => '447a020a1f875a434d62f2a401f53b82a396e494',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/php-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'orno/di' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => '~2.0',
            ),
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'df1e7fde177501eee2037dd159cf04f5f301a512',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/random_compat' => array(
            'pretty_version' => 'v9.99.100',
            'version' => '9.99.100.0',
            'reference' => '996434e5492cb4c3edcb9168db6fbb1359ef965a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/random_compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/archive_tar' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => 'b439c859564f5cbb0f64ad6002d0afe84a889602',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/archive_tar',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/console_getopt' => array(
            'pretty_version' => 'v1.4.3',
            'version' => '1.4.3.0',
            'reference' => 'a41f8d3e668987609178c7c4a9fe48fecac53fa0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/console_getopt',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear-core-minimal' => array(
            'pretty_version' => 'v1.10.16',
            'version' => '1.10.16.0',
            'reference' => 'c0f51b45f50683bf5bbf558036854ebc9b54d033',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pear/pear-core-minimal',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pear/pear_exception' => array(
            'pretty_version' => 'v1.0.2',
            'version' => '1.0.2.0',
            'reference' => 'b14fbe2ddb0b9f94f5b24cf08783d599f776fff0',
            'type' => 'class',
            'install_path' => __DIR__ . '/../pear/pear_exception',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phootwork/collection' => array(
            'pretty_version' => 'v3.2.3',
            'version' => '3.2.3.0',
            'reference' => '46dde20420fba17766c89200bc3ff91d3e58eafa',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phootwork/collection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phootwork/lang' => array(
            'pretty_version' => 'v3.2.3',
            'version' => '3.2.3.0',
            'reference' => '52ec8cce740ce1c424eef02f43b43d5ddfec7b5e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phootwork/lang',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpowermove/docblock' => array(
            'pretty_version' => 'v4.0',
            'version' => '4.0.0.0',
            'reference' => 'a73f6e17b7d4e1b92ca5378c248c952c9fae7826',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpowermove/docblock',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.3',
            'version' => '8.0.3.0',
            'reference' => '6f8d87ebd5afbf7790bde1ffc7579c7c705e0fad',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa-qrcode' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'ce4d8a729b6c93741c607cfb2217acfffb5bf76b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa-qrcode',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/cache' => array(
            'pretty_version' => '3.0.0',
            'version' => '3.0.0.0',
            'reference' => 'aa5030cfa5405eccfdcb1083ce040c2cb8d253bf',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/cache',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/container-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0',
                1 => '^1.0',
            ),
        ),
        'psr/event-dispatcher' => array(
            'pretty_version' => '1.0.0',
            'version' => '1.0.0.0',
            'reference' => 'dbefd12671e8a14ec7f180cab83036ed26714bb0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
            ),
        ),
        'psr/log' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => 'f16e1d5863e37f8d8c2a01719f5b34baa2b714d3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0|2.0|3.0',
            ),
        ),
        'psy/psysh' => array(
            'pretty_version' => 'v0.12.8',
            'version' => '0.12.8.0',
            'reference' => '85057ceedee50c49d4f6ecaff73ee96adb3b3625',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psy/psysh',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rsky/pear-core-min' => array(
            'dev_requirement' => false,
            'replaced' => array(
                0 => 'v1.10.16',
            ),
        ),
        'sabberworm/php-css-parser' => array(
            'pretty_version' => 'v8.8.0',
            'version' => '8.8.0.0',
            'reference' => '3de493bdddfd1f051249af725c7e0d2c38fed740',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sabberworm/php-css-parser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'sebastian/diff' => array(
            'pretty_version' => '4.0.6',
            'version' => '4.0.6.0',
            'reference' => 'ba01945089c3a293b01ba9badc29ad55b106b0bc',
            'type' => 'library',
            'install_path' => __DIR__ . '/../sebastian/diff',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'setasign/fpdi' => array(
            'pretty_version' => 'v2.6.3',
            'version' => '2.6.3.0',
            'reference' => '67c31f5e50c93c20579ca9e23035d8c540b51941',
            'type' => 'library',
            'install_path' => __DIR__ . '/../setasign/fpdi',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/console' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'a3011c7b7adb58d89f6c0d822abb641d7a5f9719',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/console',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/dependency-injection' => array(
            'pretty_version' => 'v6.4.20',
            'version' => '6.4.20.0',
            'reference' => 'c49796a9184a532843e78e50df9e55708b92543a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/dependency-injection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '74c71c939a79f7d5bf3c1ce9f5ea37ba0114c6f6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/error-handler' => array(
            'pretty_version' => 'v6.4.20',
            'version' => '6.4.20.0',
            'reference' => 'aa3bcf4f7674719df078e61cc8062e5b7f752031',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/error-handler',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '0ffc48080ab3e9132ea74ef4e09d8dcf26bf897e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '7642f5e970b672283b7823222ae8ef8bbc160b9f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/event-dispatcher-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/event-dispatcher-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '2.0|3.0',
            ),
        ),
        'symfony/filesystem' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => '4856c9cf585d5a0313d8d35afd681a526f038dd3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/filesystem',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/finder' => array(
            'pretty_version' => 'v6.4.17',
            'version' => '6.4.17.0',
            'reference' => '1d0e8266248c5d9ab6a87e3789e6dc482af3c9c7',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-foundation' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '3f0c7ea41db479383b81d436b836d37168fd5b99',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-foundation',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/http-kernel' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '983ca05eec6623920d24ec0f1005f487d3734a0c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/http-kernel',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mailer' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'ada2809ccd4ec27aba9fc344e3efdaec624c6438',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mailer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/mime' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'fec8aa5231f3904754955fad33c2db50594d22d1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/mime',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-iconv' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '48becf00c920479ca2e910c22a5a39e5d47ca956',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-iconv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-grapheme' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'b9123926e3b7bc2f98c02ad54f6a4b02b91a8abe',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-grapheme',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-idn' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'c36586dcf89a12315939e00ec9b4474adcb1d773',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-idn',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-intl-normalizer' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '3833d7255cc303546435cb650316bff708a1c75c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-intl-normalizer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php81' => array(
            'pretty_version' => 'v1.32.0',
            'version' => '1.32.0.0',
            'reference' => '4a4cfc2d253c21a5ad0e53071df248ed48c6ce5c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php81',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php83' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '2fb86d65e2d424369ad2905e83b236a8805ba491',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php83',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/process' => array(
            'pretty_version' => 'v6.4.20',
            'version' => '6.4.20.0',
            'reference' => 'e2a61c16af36c9a07e5c9906498b73e091949a20',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/process',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/psr-http-message-bridge' => array(
            'pretty_version' => 'v6.4.13',
            'version' => '6.4.13.0',
            'reference' => 'c9cf83326a1074f83a738fc5320945abf7fb7fec',
            'type' => 'symfony-bridge',
            'install_path' => __DIR__ . '/../symfony/psr-http-message-bridge',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/routing' => array(
            'pretty_version' => 'v6.4.18',
            'version' => '6.4.18.0',
            'reference' => 'e9bfc94953019089acdfb9be51c1b9142c4afa68',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/routing',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/serializer' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'c45f8f7763afb11e85772c0c1debb8f272c17f51',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/serializer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => 'e53260aabf78fb3d63f8d79d69ece59f80d5eda0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/service-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/service-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.1|2.0|3.0',
            ),
        ),
        'symfony/string' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '73e2c6966a5aef1d4892873ed5322245295370c6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/string',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/translation-contracts' => array(
            'pretty_version' => 'v3.5.1',
            'version' => '3.5.1.0',
            'reference' => '4667ff3bd513750603a09c8dedbea942487fb07c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/translation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/validator' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '47610116f476595b90c368ff2a22514050712785',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/validator',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-dumper' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '22560f80c0c5cd58cc0bcaf73455ffd81eb380d5',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-dumper',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/var-exporter' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => '717e7544aa99752c54ecba5c0e17459c48317472',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/var-exporter',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/yaml' => array(
            'pretty_version' => 'v6.4.21',
            'version' => '6.4.21.0',
            'reference' => 'f01987f45676778b474468aa266fe2eda1f2bc7e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/yaml',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'tecnickcom/tcpdf' => array(
            'pretty_version' => '6.10.0',
            'version' => '6.10.0.0',
            'reference' => 'ca5b6de294512145db96bcbc94e61696599c391d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../tecnickcom/tcpdf',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'twig/twig' => array(
            'pretty_version' => 'v3.19.0',
            'version' => '3.19.0.0',
            'reference' => 'd4f8c2b86374f08efc859323dbcd95c590f7124e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../twig/twig',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'webflo/drupal-finder' => array(
            'pretty_version' => '1.3.1',
            'version' => '1.3.1.0',
            'reference' => '73045060b0894c77962a10cff047f72872d8810c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../webflo/drupal-finder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
