name: CI

on: [push, pull_request]

jobs:
  unit-tests:
    runs-on: 'ubuntu-latest'
    strategy:
      matrix:
        operating-system: ['ubuntu-latest']
        php-versions: ['8.1']
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}

      - name: Install dependencies
        run: composer install

      - name: Install fixtures
        run: composer install-fixtures

      - name: Run unit tests
        run: ./vendor/bin/phpunit
