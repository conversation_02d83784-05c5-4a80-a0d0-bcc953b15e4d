{"name": "myclabs/deep-copy", "description": "Create deep copies (clones) of your objects", "license": "MIT", "type": "library", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpspec/prophecy": "^1.10", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3 <3.2.2"}, "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "autoload-dev": {"psr-4": {"DeepCopyTest\\": "tests/DeepCopyTest/", "DeepCopy\\": "fixtures/"}}, "config": {"sort-packages": true}}