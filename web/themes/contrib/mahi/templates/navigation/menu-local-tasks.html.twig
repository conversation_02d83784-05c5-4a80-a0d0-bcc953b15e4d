{#
/**
 * @file
 * Theme override to display primary and secondary local tasks.
 *
 * Available variables:
 * - primary: HTML list items representing primary tasks.
 * - secondary: HTML list items representing primary tasks.
 *
 * Each item in these variables (primary and secondary) can be individually
 * themed in menu-local-task.html.twig.
 */
#}
{% if primary %}
  <h2 class="visually-hidden">{{ 'Primary tabs'|t }}</h2>
  <nav role="navigation" aria-labelledby="primary-tabs-title" data-drupal-nav-primary-tabs>
    <ul class="page-tabs primary-tab">{{ primary }}</ul>
  </nav>
{% endif %}
{% if secondary %}
  <h2 class="visually-hidden">{{ 'Secondary tabs'|t }}</h2>
  <nav role="navigation" aria-labelledby="secondary-tabs-title">
    <ul class="page-tabs secondary-tab">{{ secondary }}</ul>
  </nav>
{% endif %}