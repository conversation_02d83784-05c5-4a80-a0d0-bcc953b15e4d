{#
/**
 * @file
 * Theme override to navigate books.
 *
 * Presented under nodes that are a part of book outlines.
 *
 * Available variables:
 * - tree: The immediate children of the current node rendered as an unordered
 *   list.
 * - current_depth: Depth of the current node within the book outline. Provided
 *   for context.
 * - prev_url: URL to the previous node.
 * - prev_title: Title of the previous node.
 * - parent_url: URL to the parent node.
 * - parent_title: Title of the parent node. Not printed by default. Provided
 *   as an option.
 * - next_url: URL to the next node.
 * - next_title: Title of the next node.
 * - has_links: Flags TRUE whenever the previous, parent or next data has a
 *   value.
 * - book_id: The book ID of the current outline being viewed. Same as the node
 *   ID containing the entire outline. Provided for context.
 * - book_url: The book/node URL of the current outline being viewed. Provided
 *   as an option. Not used by default.
 * - book_title: The book/node title of the current outline being viewed.
 *
 * @see template_preprocess_book_navigation()
 */
#}
{{ attach_library('mahi/book') }}
{% if tree or has_links %}
  <nav class="book-navigation" role="navigation" aria-labelledby="book-label-{{ book_id }}">
    {{ tree }}
    {% if has_links %}
      <h2 class="visually-hidden">{{ 'Book traversal links for'|t }} {{ book_title }}</h2>
      <ul class="book-pager dark">
      {% if prev_url %}
        <li class="book-pager-item book-pager-item-previous">
          <a href="{{ prev_url }}" rel="prev" title="{{ 'Go to previous page'|t }}"><i class="icon-arrow-left"></i> {{ prev_title }}</a>
        </li>
      {% endif %}
      {% if parent_url %}
        <li class="book-pager-item book-pager-item-center">
          <a href="{{ parent_url }}" title="{{ 'Go to parent page'|t }}">{{ 'Up'|t }}</a>
        </li>
      {% endif %}
      {% if next_url %}
        <li class="book-pager-item book-pager-item-next">
          <a href="{{ next_url }}" rel="next" title="{{ 'Go to next page'|t }}">{{ next_title }} <i class="icon-arrow-right" aria-hidden="true"></i></a>
        </li>
      {% endif %}
    </ul>
    {% endif %}
  </nav>
{% endif %}
