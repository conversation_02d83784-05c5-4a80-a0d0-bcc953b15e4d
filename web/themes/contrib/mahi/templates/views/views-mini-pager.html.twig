{#
/**
 * @file
 * Theme override for a views mini-pager.
 *
 * Available variables:
 * - heading_id: Pagination heading ID.
 * - items: List of pager items.
 *
 * @see template_preprocess_views_mini_pager()
 */
#}
{% if items.previous or items.next %}
  <nav class="mini-pager" role="navigation" aria-labelledby="{{ heading_id }}">
    <h4 id="{{ heading_id }}" class="visually-hidden">{{ 'Pagination'|t }}</h4>
    <ul class="pager-items">
      {# Print previous item if we are not on the first page. #}
      {% if items.previous %}
        {% apply spaceless %}
          <li class="pager-item pager-item-arrow pager-item-previous">
            <a href="{{ items.previous.href }}" class="pager-item-link" title="{{ 'Go to previous page'|t }}" rel="prev"{{ items.previous.attributes|without('href', 'title', 'rel', 'class') }}>
              <span class="visually-hidden">{{ 'Previous page'|t }}</span>
              <i class="icon-angle-left"></i>
            </a>
          </li>
        {% endapply %}
      {% endif %}

      {# Print current active page. #}
      {% if items.current %}
        <li class="pager-item pager-item-active">
          {{ items.current }}
        </li>
      {% endif %}

      {# Print next item if we are not on the last page. #}
      {% if items.next %}
        {% apply spaceless %}
          <li class="pager-item pager-item-arrow pager-item-next">
            <a href="{{ items.next.href }}" class="pager__link" title="{{ 'Go to next page'|t }}" rel="next"{{ items.next.attributes|without('href', 'title', 'rel') }}>
              <span class="visually-hidden">{{ 'Next page'|t }}</span>
              <i class="icon-angle-right"></i>
            </a>
          </li>
        {% endapply %}
      {% endif %}
    </ul>
  </nav>
{% endif %}
