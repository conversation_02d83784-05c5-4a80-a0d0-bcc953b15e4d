{% if fontawesome_five %}
  {{ attach_library('mahi/fontawesome5') }}
{% endif %}
{% if fontawesome_six %}
  {{ attach_library('mahi/fontawesome6') }}
{% endif %}
{% if bootstrapicons %}
  {{ attach_library('mahi/bootstrap-icons') }}
{% endif %}

<style>
.container {
  max-width: {{ container_width }}px;
}
{% if header_width == 'header_width_full' %}
.header .container {
  max-width: 100%;
}
{% endif %}
{% if main_width == 'main_width_full' %}
.highlighted .container,
.main-wrapper .container {
  max-width: 100%;
}
{% endif %}

{% if footer_width == 'footer_width_full' %}
.footer .container {
  max-width: 100%;
}
{% endif %}
{% if highlight_author_comment %}
.by-node-author {
  box-shadow: 0 0 0.4rem var(--color-primary);
}
{% endif %}
</style>
