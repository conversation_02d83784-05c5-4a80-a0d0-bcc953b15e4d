{#
/**
 * @file
 * Theme override for the basic structure of a single Drupal page.
 *
 * Variables:
 * - logged_in: A flag indicating if user is logged in.
 * - root_path: The root path of the current page (e.g., node, admin, user).
 * - node_type: The content type for the current node, if the page is a node.
 * - head_title: List of text elements that make up the head_title variable.
 *   May contain one or more of the following:
 *   - title: The title of the page.
 *   - name: The name of the site.
 *   - slogan: The slogan of the site.
 * - page_top: Initial rendered markup. This should be printed before 'page'.
 * - page: The rendered page markup.
 * - page_bottom: Closing rendered markup. This variable should be printed after
 *   'page'.
 * - db_offline: A flag indicating if the database is offline.
 * - placeholder_token: The token for generating head, css, js and js-bottom
 *   placeholders.
 * - noscript_styles: <noscript> content.
 *
 * @see template_preprocess_html()
 */
#}
{%
  set body_classes = [
    logged_in ? 'user-logged-in' : 'user-guest',
    not root_path ? 'frontpage' : 'inner-page path-' ~ root_path|clean_class,
    node_type ? 'page-type-' ~ node_type|clean_class,
    not page.sidebar_first and not page.sidebar_second ? 'no-sidebar',
    page.sidebar_first and not page.sidebar_second ? 'one-sidebar sidebar-left',
    page.sidebar_second and not page.sidebar_first ? 'one-sidebar sidebar-right',
    page.sidebar_first and page.sidebar_second ? 'two-sidebar'
  ]
%}
<!DOCTYPE html>
<html{{ html_attributes }}>
  <head>
    <head-placeholder token="{{ placeholder_token }}">
    <title>{{ head_title|safe_join(' | ') }}</title>
    {% if font_src == 'local' %}
      <link rel="preload" as="font" href="{{ base_path ~ directory }}/fonts/poppins.woff2" type="font/woff2" crossorigin>
      <link rel="preload" as="font" href="{{ base_path ~ directory }}/fonts/plus-jakarta-sans.woff2" type="font/woff2" crossorigin>
    {{ attach_library('mahi/googlefontslocal') }}
    {% elseif font_src == 'googlecdn' %}
      <link rel="preconnect" href="https://fonts.googleapis.com">
      <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
      <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@700&family=Poppins&display=swap" rel="stylesheet">
    {% endif %}
    <css-placeholder token="{{ placeholder_token }}">
    <js-placeholder token="{{ placeholder_token }}">
{% if css_extra %}
<style>
{{ css_code }}
</style>
{% endif %}
  </head>
  <body{{ attributes.addClass(body_classes) }}>
    {#
      Keyboard navigation/accessibility link to main content section in
      page.html.twig.
    #}
    <a href="#main-content" class="visually-hidden focusable">
      {{ 'Skip to main content'|t }}
    </a>
    {{ page_top }}
    {{ page }}
    {{ page_bottom }}
    <js-bottom-placeholder token="{{ placeholder_token }}">
    {% include '@mahi/parts/settings.html.twig' %}
  </body>
</html>
