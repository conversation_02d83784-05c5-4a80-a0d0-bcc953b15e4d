{#
/**
 * @file
 * Theme override to display a single page.
 *
 * The doctype, html, head and body tags are not in this template. Instead they
 * can be found in the html.html.twig template in this directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - base_path: The base URL path of the Dr<PERSON>al installation. Will usually be
 *   "/" unless you have installed <PERSON><PERSON><PERSON> in a sub-directory.
 * - is_front: A flag indicating if the current page is the front page.
 * - logged_in: A flag indicating if the user is registered and signed in.
 * - is_admin: A flag indicating if the user has permission to access
 *   administration pages.
 *
 * Site identity:
 * - front_page: The URL of the front page. Use this instead of base_path when
 *   linking to the front page. This includes the language domain or prefix.
 *
 * Page content (in order of occurrence in the default page.html.twig):
 * - messages: Status and error messages. Should be displayed prominently.
 * - node: Fully loaded node, if there is an automatically-loaded node
 *   associated with the page and the node ID is the second argument in the
 *   page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * @see template_preprocess_page()
 * @see html.html.twig
 */
#}
{% include '@mahi/parts/header.html.twig' %}
{% include '@mahi/parts/highlighted.html.twig' %}
<div id="main-wrapper" class="main-wrapper">
  <div class="container">
    <div class="main-container">
      <main id="main" class="page-content">
        <a id="main-content" tabindex="-1"></a>{# link is in html.html.twig #}
        {% if is_front and page.content_home %}
          {% include '@mahi/parts/content_home.html.twig' %}
        {% endif %}
        {% include '@mahi/parts/content_top.html.twig' %}
        {{ page.content }}
        {% include '@mahi/parts/content_bottom.html.twig' %}
      </main>
      {% include '@mahi/parts/sidebar-left.html.twig' %}
      {% include '@mahi/parts/sidebar-right.html.twig' %}
    </div> {# /.main-container #}
  </div> {# /.container #}
</div>{# /main-wrapper #}
{% include '@mahi/parts/footer.html.twig' %}
