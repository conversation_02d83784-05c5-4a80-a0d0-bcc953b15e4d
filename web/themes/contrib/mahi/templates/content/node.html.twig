{#
/**
 * @file
 * <PERSON>'s theme implementation to display a node.
 *
 * Available variables:
 * - node: The node entity with limited access to object properties and methods.
 *   Only method names starting with "get", "has", or "is" and a few common
 *   methods such as "id", "label", and "bundle" are available. For example:
 *   - node.getCreatedTime() will return the node creation timestamp.
 *   - node.hasField('field_example') returns TRUE if the node bundle includes
 *     field_example. (This does not indicate the presence of a value in this
 *     field.)
 *   - node.isPublished() will return whether the node is published or not.
 *   Calling other methods, such as node.delete(), will result in an exception.
 *   See \Drupal\node\Entity\Node for a full list of public properties and
 *   methods for the node object.
 * - label: The title of the node.
 * - content: All node items. Use {{ content }} to print them all,
 *   or print a subset such as {{ content.field_example }}. Use
 *   {{ content|without('field_example') }} to temporarily suppress the printing
 *   of a given child element.
 * - author_picture: The node author user entity, rendered using the "compact"
 *   view mode.
 * - metadata: Metadata for this node.
 * - date: Themed creation date field.
 * - author_name: Themed author name field.
 * - url: Direct URL of the current node.
 * - display_submitted: Whether submission information should be displayed.
 * - attributes: HTML attributes for the containing element.
 *   The attributes.class element may contain one or more of the following
 *   classes:
 *   - node: The current template type (also known as a "theming hook").
 *   - node--type-[type]: The current node type. For example, if the node is an
 *     "Article" it would result in "node--type-article". Note that the machine
 *     name will often be in a short form of the human readable label.
 *   - node--view-mode-[view_mode]: The View Mode of the node; for example, a
 *     teaser would result in: "node--view-mode-teaser", and
 *     full: "node--view-mode-full".
 *   The following are controlled through the node publishing options.
 *   - node--promoted: Appears on nodes promoted to the front page.
 *   - node--sticky: Appears on nodes ordered above other non-sticky nodes in
 *     teaser listings.
 *   - node--unpublished: Appears on unpublished nodes visible only to site
 *     admins.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main
 *   content tag that appears in the template.
 * - author_attributes: Same as attributes, except applied to the author of
 *   the node tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 * - view_mode: View mode; for example, "teaser" or "full".
 * - teaser: Flag for the teaser state. Will be true if view_mode is 'teaser'.
 * - page: Flag for the full page state. Will be true if view_mode is 'full'.
 * - readmore: Flag for more state. Will be true if the teaser content of the
 *   node cannot hold the main body content.
 * - logged_in: Flag for authenticated user status. Will be true when the
 *   current user is a logged-in member.
 * - is_admin: Flag for admin user status. Will be true when the current user
 *   is an administrator.
 *
 * @see template_preprocess_node()
 */
#}
{%
set classes = [
  'node',
  'node-type-' ~ node.bundle|clean_class,
  node.isPromoted() ? 'node-promoted',
  node.isSticky() ? 'node-sticky',
  not node.isPublished() ? 'node-unpublished',
  view_mode ? 'node-view-mode-' ~ view_mode|clean_class,
]
%}
<article{{ attributes.addClass(classes) }}>
  <header class="node-header">
    {{ title_prefix }}
    {% if label and not page %}
       <h2{{ title_attributes.addClass('node-title') }}>
          <a href="{{ url }}" rel="bookmark">{{ label }}</a>
        </h2>
    {% endif %}
    {{ title_suffix }}
    {% if display_submitted %}
      <div class="node-meta dark">
        {% if author_picture %}
          <div class="node-author-img">
            {{ author_picture }}
          </div>
        {% endif %}
        <div{{ author_attributes.addClass('node-author-date') }}>
          {% set createdDate = node.getCreatedTime|date('F j, Y') %}
          <div class="node-author"><i class="icon-user"></i>{{ 'By'|t }} {{ author_name }}</div><div class="node-date"><i class="icon-calendar"></i>{{ createdDate }}</div>
          {{ metadata }}
        </div>
      </div>
    {% endif %}
  </header>
  <div{{ content_attributes.addClass('node-content') }}>
    {{ content }}
  </div>
</article>
