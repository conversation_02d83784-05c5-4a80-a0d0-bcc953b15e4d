{#
/**
 * @file
 * Theme override for status messages.
 *
 * Displays status, error, and warning messages, grouped by type.
 *
 * An invisible heading identifies the messages for assistive technology.
 * Sighted users see a colored box. See http://www.w3.org/TR/WCAG-TECHS/H69.html
 * for info.
 *
 * Add an ARIA label to the contentinfo area so that assistive technology
 * user agents will better describe this landmark.
 *
 * Available variables:
 * - message_list: List of messages to be displayed, grouped by type.
 * - status_headings: List of all status types.
 * - display: (optional) May have a value of 'status' or 'error' when only
 *   displaying messages of that specific type.
 * - attributes: HTML attributes for the element, including:
 *   - class: HTML classes.
 */
#}
{% for type, messages in message_list %}
{%
  set classes = [
    'message',
    'message-' ~ type,
  ]
%}
  <div role="contentinfo" aria-label="{{ status_headings[type] }}" data-drupal-selector="messages" {{ attributes.addClass(classes) }}>
    {% if type == 'error' %}
      <div role="alert" class="status-error">
    {% endif %}
      {% if status_headings[type] %}
        <h2 class="visually-hidden">{{ status_headings[type] }}</h2>
      {% endif %}
      {% if messages|length > 1 %}
        <ul class="status-message-list">
          {% for message in messages %}
            <li class="message-item">{{ message }}</li>
          {% endfor %}
        </ul>
      {% else %}
        {{ messages|first }}
      {% endif %}
    {% if type == 'error' %}
      </div>
    {% endif %}
  </div>
{% endfor %}
