{% extends "item-list.html.twig" %}
{#
/**
 * @file
 * Theme override for an item list of search results.
 *
 * Available variables:
 * - items: A list of items. Each item contains:
 *   - attributes: HTML attributes to be applied to each list item.
 *   - value: The content of the list element.
 * - title: The title of the list.
 * - list_type: The tag for list element ("ul" or "ol").
 * - attributes: HTML attributes to be applied to the list.
 * - empty: A message to display when there are no items. Allowed value is a
 *   string or render array.
 * - context: An list of contextual data associated with the list. For search
 *   results, the following data is set:
 *   - plugin: The search plugin ID, for example "node_search".
 *
 * @see template_preprocess_item_list()
 */
#}
{%
  set classes = [
    'search-results',
    context.plugin ~ '-results',
  ]
%}
{% set attributes = attributes.addClass(classes) %}
