.comments {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-top: 0.4rem;
  border-top: 4px double var(--border);
}
/* Single comment */
.single-comment {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background-color: var(--light);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 0 0.4rem var(--border);
}
.single-comment-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid var(--border);
}
.comment-picture {
  background-color: var(--border);
  width: 4rem;
  height: 4rem;
  border-radius: 8px;
}
.comment-header {
  display: flex;
  flex-direction: column;
}
.single-comment-title {
  font-size: 1.2rem;
  margin: 0;
}
.comment-body p:last-of-type {
  margin-bottom: 1.2rem;
}
.comment-body .links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  list-style: none;
  margin: 0;
  padding: 0;
}
.comment-body .links a {
  padding: 5px 1rem;
  border-radius: 2rem;
  box-shadow: 0 0 3px 1px var(--color-secondary);
  transition: all 0.2s ease;
}
.comment-body .links a:hover {
  box-shadow: 0 0 4px 1px var(--color-primary);
}
.comments .indented {
  margin-left: 2rem;
}
/* comment form */
.comment-form {
  background-color: var(--light);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: 0 0 0.4rem var(--border);
}