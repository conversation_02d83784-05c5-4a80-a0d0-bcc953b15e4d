@media (max-width: 1023px) {
  .primary-menu-wrapper {
    width: 90%;
    max-width: 320px;
    height: 100%;
    overflow-y: auto;
  }
}

@media (min-width: 576px) {
  .w10,
  .w20,
  .w30,
  .w40,
  .w50,
  .w60,
  .w70,
  .w80,
  .w90 {
    flex-basis: calc(50% - 1rem);
  }
}
@media (min-width: 768px) {
  html {
    font-size: 18px;
  }
  /* page layout */
  .sidebar-left .main-container {
    grid-template-columns: 1fr 3fr;
  }
  .sidebar-right .main-container {
    grid-template-columns: 3fr 1fr;
  }
  .two-sidebar .main-container {
    grid-template-columns: 1fr 2fr 1fr;
  }
  #sidebar-left {
    order: 1;
  }
  #main {
    order: 2;
  }
  #sidebar-right {
    order: 3;
  }
  /* Header */
  .header-right {
    gap: 2rem;
  }
  .site-brand img {
    max-height: 60px;
  }
  /* Node */
  .node-meta {
    font-size: 1rem;
  }
  /* Content width */
  .width30 {
    width: 30%;
  }
  .width40 {
    width: 40%;
  }
  .width50 {
    width: 50%;
  }
  .width60 {
    width: 60%;
  }
  .width70 {
    width: 70%;
  }
  .width80 {
    width: 80%;
  }
  .width90 {
    width: 90%;
  }
  .w10 {
    flex-basis: calc(10% - 1rem);
  }
  .w20 {
    flex-basis: calc(20% - 1rem);
  }
  .w30 {
    flex-basis: calc(30% - 1rem);
  }
  .w40 {
    flex-basis: calc(40% - 1rem);
  }
  .w50 {
    flex-basis: calc(50% - 1rem);
  }
  .w60 {
    flex-basis: calc(60% - 1rem);
  }
  .w70 {
    flex-basis: calc(70% - 1rem);
  }
  .w80 {
    flex-basis: calc(80% - 1rem);
  }
  .w90 {
    flex-basis: calc(90% - 1rem);
  }
}
@media (min-width: 1024px) {
  /* Header main menu */
  .primary-menu-wrapper {
    position: relative;
    background-color: transparent;
    padding: 0;
    transform: translateX(0);
    z-index: 2;
  }
  .region-primary-menu .menu {
    flex-direction: row;
    gap: 2rem;
  }
  .region-primary-menu .menu > li {
    position: relative;
    padding: 0;
    border: 0;
  }
  .region-primary-menu .menu > li::after {
    position: absolute;
    content: '';
    background-color: var(--color-primary);
    width: 1rem;
    height: 2px;
    left: calc(50% - 0.5rem);
    bottom: 0;
    transition: 0.3s linear;
  }
  .region-primary-menu .menu > li:hover::after {
    width: 100%;
    left: 0;
  }
  .menu-item-has-children::before {
    right: -0.8rem;
  }
  .region-primary-menu .submenu {
    position: absolute;
    background-color: var(--color-primary);
    top: calc(100% - 2px);
    min-width: 200px;
    padding: 0 0.8rem;
    border-radius: 0 0 8px 8px;
    opacity: 0;
    visibility: hidden;
  }
  .region-primary-menu .submenu li {
    padding: 0;
  }
  .region-primary-menu .submenu li:first-child {
    border-top: none;
  }
  .region-primary-menu .submenu a {
   padding: 0.5rem 0;
  }
  .menu-item-has-children:hover > .submenu {
    visibility: visible;
    animation: slideUp 0.5s forwards;
  }
  .region-primary-menu .submenu a:hover {
    color: var(--dark);
  }
  /* third level menu */
  .region-primary-menu .submenu .submenu {
    left: 100%;
    top: 0;
  }
  .submenu .menu-item-has-children::before {
    right: 0;
    color: #ffffff;
  }
  .mobile-menu-icon,
  .close-mobile-menu {
    display: none;
  }
}
