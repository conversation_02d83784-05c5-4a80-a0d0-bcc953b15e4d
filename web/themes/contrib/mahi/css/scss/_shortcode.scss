/* Color
--------------------------- */
.color-primary {
  color: var(--color-primary);
}
.color-secondary {
  color: var(--color-secondary);
}
.color-dark {
  color: var(--dark);
}
.color-white {
  color: #ffffff;
}
/* Text align
--------------------------- */
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.center {
  margin: 0 auto;
}
/* Inline content
------------------------- */
.inline {
  display: inline-block;
}
.inline:not(:last-child) {
  padding-right: 1rem;
}
/* Margin Padding
------------------------- */
.no-margin {
  margin: 0;
}
.no-paddibng {
  padding: 0;
}
/* Content direction
------------------------- */
.rtl {
  direction: rtl;
}
.ltr {
  direction: ltr;
}
/* Text Size
-------------------------------------------- */
.size-small {
  font-size: 0.75rem;
}
.size-large {
  font-size: 1.5rem;
}
.size-2x {
  font-size: 2rem;
}
.size-3x {
  font-size: 3rem;
}
.size-4x {
  font-size: 4rem;
}
.size-5x {
  font-size: 5rem;
}
.size-6x {
  font-size: 6rem;
}
.size-7x {
  font-size: 7rem;
}
.size-8x {
  font-size: 8rem;
}
/* Content width
------------------------- */
.width30,
.width40,
.width50,
.width60,
.width70,
.width80,
.width90 {
  width: 100%;
  clear: both;
  display: block;
}
/* Empty width and height
------------------------- */
.w20px {
  display: inline-block;
  width: 20px;
}
.w30px {
  display: inline-block;
  width: 30px;
}
.w40px {
  display: inline-block;
  width: 40px;
}
.w50px {
  display: inline-block;
  width: 50px;
}
.w70px {
  display: inline-block;
  width: 70px;
}
.w100px {
  display: inline-block;
  width: 100px;
}
.spacer,
.spacer-small,
.spacer-x2,
.spacer-x3 {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}
.spacer {
  padding: 1rem 0;
}
.spacer-small {
  padding: 0.5rem 0;
}
.spacer-x2 {
  padding: 2rem 0;
}
.spacer-x3 {
  padding: 3rem 0;
}
/* Responsive Columns
------------------------- */
.section,
.section-small,
.section-large {
  display: block;
  width: 100%;
}
.section {
  padding: 2rem 0;
}
.section-small {
  padding: 1rem 0;
}
.section-large {
  padding: 3rem 0;
}
.flex {
  display: flex;
  margin: 0;
  padding: 0;
}
.grid {
  display: grid;
  margin: 0;
  padding: 0;
}
.items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(265px, 1fr));
  margin: 0;
  padding: 0;
  list-style: none;
}
.item img {
  display: block;
}
.columns {
  display: flex;
  flex-wrap: wrap;
  margin: 0;
  padding: 0;
  list-style: none;
}
/* Create Equal width columns with no gap */
.column {
  flex: 1 1 230px;
  margin: 0;
  padding: 0;
}
/* Flex and grid properties
------------------------- */
.space-between {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}
.v-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.h-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}
.vh-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}
.gap {
  gap: 1rem;
}
.gap-2x {
  gap: 2rem;
}
.gap-small {
  gap: 0.5rem;
}
.no-gap {
  gap: 0;
}
/* Flex properties */
.flex-row {
  flex-direction: row;
}
.flex-column {
  flex-direction: column;
}
.w10,
.w20,
.w30,
.w40,
.w50,
.w60,
.w70,
.w80,
.w90 {
  flex: 1 1 100%;
}
.wrap {
  flex-wrap: wrap;
}
.no-wrap {
  flex-wrap: nowrap;
}
/* Box
------------------------- */
.box {
  background-color: var(--light);
  padding: 1rem;
  border-radius: 8px;
  box-shadow: var(--shadow);
}
.box p:last-of-type {
  margin: 0;
}
/* Responsive view
------------------------- */
.view-in-mobile {
  display: block;
}
.view-in-desktop {
  display: none;
}
