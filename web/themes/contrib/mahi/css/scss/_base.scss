/* Variables
----------------------- */
:root {
  --color-primary: #f1426c;
  --color-secondary: #e9ab0d;
  --color-primary-light: #fff0f4;
  --bg-body: #ffffff;
  --text-color: #4a4a4a;
  --bold-color: #222222;
  --light: #fffbf6;
  --dark: #1f2433;
  --dark-text-color: #c3c3c3;
  --border: #b5b6be;
  --border-dark: #3d4353;
  --shadow: 0 0 3px 0 var(--color-secondary);
  --font-text: "Poppins", sans-serif;
  --font-heading: "Plus Jakarta Sans", sans-serif;
}
/* Default Box sizing */
*,
*::before,
*::after {
  box-sizing: border-box;
}
/* Remove default margin */
body,
p,
figure,
blockquote,
dl,
dd {
  margin: 0;
}
/* HTML and Body
----------------------- */
html:focus-within {
  scroll-behavior: smooth;
}
html {
  font-size: 16px;
  scroll-behavior: smooth;
}
html,
body {
  height: 100%;
}
body {
  background-color: var(--bg-body);
  color: var(--text-color);
  font-family: var(--font-text), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-size: 1rem;
  font-weight: 400;
  min-height: 100vh;
  line-height: 1.6;
  overflow-x: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}
/* Regions
----------------------- */
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
menu,
nav,
section {
  display: block;
}
template,
[hidden] {
  display: none;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
/* Typography
----------------------- */
/* Typography -> Headings */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: var(--bold-color);
  font-family: var(--font-heading);
  font-weight: 700;
  font-style: normal;
  line-height: 1.4;
  margin: 0 0 0.5rem 0;
}
h1 {
  font-size: 2.1rem;
}
h2 {
  font-size: 1.8rem;
}
h3 {
  font-size: 1.5rem;
}
h4 {
  font-size: 1.2rem;
}
h5, h6 {
  font-size: 1.1rem;
  text-transform: uppercase;
}
/* Typography -> Paragraph */
p {
  margin: 0 0 1.2rem 0;
}
/* Typography -> Links */
a:not([class]) {
  text-decoration-skip-ink: auto;
}
a {
  color: var(--color-primary);
  background-color: transparent;
  text-decoration: none;
  -webkit-transition: color 0.4s ease;
  -moz-transition: color 0.4s ease;
  transition: color 0.4s ease;
}
a:active,
a:hover,
a:focus {
  background-color: transparent;
  text-decoration: none;
  border: 0;
  outline: 0;
}
a:hover {
  color: var(--color-secondary);
}
a:active,
li a.active {
  color: var(--color-primary);
}

/* Typography -> Abbreviation */
abbr[title] {
  border-bottom: none;
  text-decoration: underline;
  text-decoration: underline dotted;
}
abbr {
  cursor: help;
}
acronym {
  border-bottom: 1px dotted;
  cursor: help;
}

/* Typography -> Text styling */
b,
strong {
  font-weight: bolder;
  color: var(--bold-color);
}
em,
dfn,
cite {
  font-style: italic;
}
mark,
ins {
  padding: 4px 8px;
  background: var(--color-primary-light);
}
del {
  text-decoration: line-through;
}
small {
  font-size: 80%;
}
big {
  font-size: 125%;
}
sub,
sup {
  position: relative;
  font-size: 75%;
  line-height: 0;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
tt,
var {
  font-family: monospace, monospace;
  font-style: italic;
}

/* Typography -> Blockquote */
blockquote {
  position: relative;
  background-color: #ffffff;
  color: var(--bold-color);
  margin-bottom: 1.2rem;
  padding: 1.8rem;
  border-radius: 8px;
  box-shadow: var(--shadow);
  isolation: isolate;
  z-index: 1;
}
blockquote:before {
  position: absolute;
  top: 0;
  left: 0;
  content: '\e014';
  font-family: "mahi";
  color: var(--color-primary-light);
  font-size: 6rem;
  line-height: 1;
  z-index: -1;
}
blockquote > p:last-child {
  margin-bottom: 0;
}
blockquote cite {
  color: var(--color-primary);
}
/* Typography -> HTML code tags */
samp {
  background-color: var(--light);
  margin: 1rem 0;
  padding: 4px 10px;
  font-family: monospace, monospace;
  font-size: 1rem;
  border: 1px solid var(--border);
}
code {
  background-color: var(--dark);
  color: #ffffff;
  font-family: monospace, monospace;
  padding: 2px 10px;
  font-size: 1rem;
}
pre {
  background-color: var(--dark);
  color: #ffffff;
  font-size: 1rem;
  margin: 1rem 0;
  padding: 1rem;
  tab-size: 2;
  overflow-x: auto;
}
kbd {
  background-color: var(--color-primary-light);
  padding: 4px 10px;
  font-family: monospace, monospace;
  font-size: 1rem;
}
/* Typography -> Address */
address {
  margin: 0 0 1.75rem;
  font-style: italic;
}

/* Typography -> Description Lists */
dl {
  margin: 0 0 1.75rem;
}
dt {
  color: var(--bold-color);
  font-family: var(--font-heading);
  font-weight: 700;
}
dd {
  margin: 0 0 1.75rem;
}

/* Typography -> HTML Elements */
hr {
  clear: both;
  width: 100%;
  height: 2px;
  margin: 0.5rem 0;
  background: var(--border);
  border: 0;
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

/* Forms
----------------------- */
form {
  margin-bottom: 1rem;
}
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: 100%;
  line-height: 1.6;
}
button,
input {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  cursor: pointer;
  -webkit-appearance: button;
}
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  padding: 0;
  border: 0;
  border-style: none;
}
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 0;
}
button[disabled],
html input[disabled] {
  cursor: not-allowed;
  opacity: 0.7;
}
::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}
input {
  line-height: normal;
}
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="url"] {
  background-color: #ffffff;
  padding: 10px;
  max-width: 100%;
  border: 2px solid var(--color-primary-light);
  border-radius: 4px;
  -webkit-transition: all 0.3s linear;
  -moz-transition: all 0.3s linear;
  transition: all 0.3s linear;
}
input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="url"]:focus,
textarea:focus {
  background-color: var(--light);
  outline: 0;
}

input[type="submit"],
input[type="button"],
input[type="reset"] {
  position: relative;
  display: inline-block;
  background-color: var(--color-primary);
  color: #ffffff;
  padding: 0.5rem 1.2rem;
  border: 0;
  border-radius: 8px;
  transition: all 0.4s ease-in-out;
}
input[type="submit"]:hover,
input[type="button"]:hover,
input[type="reset"]:hover {
  background-color: var(--color-secondary);
}
input[type="checkbox"],
input[type="radio"] {
  margin-right: 6px;
  padding: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
  -moz-appearance: textfield;
}
textarea {
  width: 100%;
  max-width: 100%;
  padding: 1rem;
 border: 2px solid var(--color-primary-light);
  border-radius: 4px;
  overflow: auto;
  -webkit-transition: border 0.3s linear;
  -moz-transition: border 0.3s linear;
  transition: border 0.3s linear;
  vertical-align: top;
}
fieldset {
  margin: 0 0 10px 0;
  padding: 1rem;
  border: 1px solid var(--border);
}
fieldset > :last-child {
  margin-bottom: 0;
}
legend {
  display: table;
  max-width: 100%;
  padding: 0;
  color: inherit;
  border: 0;
  box-sizing: border-box;
  white-space: normal;
}
optgroup {
  font-weight: bold;
}
select {
  padding: 4px 0;
}
form label {
  font-weight: bold;
}
label[for] {
  cursor: pointer;
}
.page-content input[type="text"],
.page-content input[type="password"],
.page-content input[type="search"] {
  padding: 9px 6px;
  outline: 0;
}

/* Drupal form elements */
.form-item {
  margin-bottom: 1rem;
}
.form-required:after {
  content: "*";
  display: inline-block;
  color: var(--color-primary);
  padding-left: 4px;
  font-size: 0.8em;
  vertical-align: super;
}
.form-item label {
  display: block;
}
label.option {
  display: inline;
  font-weight: normal;
}
form .description {
  color: var(--border);
  font-size: 0.9rem;
}
/* placeholder */
::-webkit-input-placeholder {
  color: #8a8a8a;
}
:-moz-placeholder {
  color: #8a8a8a;
}
::-moz-placeholder {
  color: #8a8a8a;
  opacity: 1;
}
:-ms-input-placeholder {
  color: #8a8a8a;
}

/* List
----------------------- */
ul,
ol {
  margin: 0;
  padding: 0 0 0.25rem 1rem; /* LTR */
}
[dir="rtl"] ul,
[dir="rtl"] ol {
  padding: 0 1rem 0.25rem 0;
}
ol ol,
ul ul {
  margin: 0;
  padding: 0 0 0.25rem 1rem; /* LTR */
}
[dir="rtl"] ol ol,
[dir="rtl"] ul ul {
  padding: 0 1rem 0.25rem 0;
}
ul {
  list-style: disc;
}
li > ul,
li > ol {
  margin-bottom: 0;
}
[dir="rtl"] ul,
[dir="rtl"] ol {
  padding: 0 1em 0.25em 0;
}
/* Table
----------------------- */
table {
  width: 100%;
  margin-bottom: 1.2rem;
  border-spacing: 0;
  border-collapse: collapse;
}
th,
tr,
td {
  vertical-align: middle;
}
th {
  background-color: var(--dark);
  font-family: var(--font-heading);
  color: #ffffff;
  margin: 0;
  padding: 10px;
  border: 1px solid var(--color-primary);
  text-align: left;
}
th a {
  color: #ffffff;
}
td {
  padding: 5px 10px;
  border: 1px solid var(--color-primary);
}

/* Media
----------------------- */
img,
picture,
svg {
  max-width: 100%;
  height: auto;
  margin: 0;
  padding: 0;
  border: 0;
  vertical-align: middle;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border: 0;
}
figcaption {
  padding: 4px;
  font-size: 0.8rem;
  background: #ffffff;
  border: 1px solid var(--border);
  text-align: center;
}
.align-left,
img.align-left,
figure.align-left {
  float: left;
  margin: 20px 20px 20px 0;
}
.align-right,
img.align-right,
figure.align-right {
  float: right;
  margin: 20px 0 20px 20px;
}
.align-center,
img.align-center,
figure.align-center {
  display: block;
  clear: both;
  margin: 20px auto;
}
figure.align-center {
  display: table;
}
figure.align-center img {
  display: block;
  clear: both;
  margin: 0 auto;
}
/* Drupal image filed */
.feed-icon {
  display: block;
}
details {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid var(--border);
}
summary {
  background-color: #ffffff;
  color: var(--text-color);
  padding: 0.5rem;
  cursor: pointer;
}

/* Misc
----------------------- */
::selection {
  background: var(--color-primary);
  color: #ffffff;
}
::-moz-selection {
  background: var(--color-primary);
  color: #ffffff;
}
