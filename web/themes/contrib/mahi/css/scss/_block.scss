/* Block Regions
--------------------------*/
.block-title {
  position: relative;
}

/* Breadcrumb
--------------------------*/
.breadcrumb-items {
  margin: 0;
  padding: 0;
  list-style: none;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
ol.breadcrumb-items li {
  padding: 0;
}
.breadcrumb-item a {
  position: relative;
}
.breadcrumb-item a::after {
  content: ">";
  color: var(--dark-text-color);
  padding: 0 10px;
}
/* Highlight region */
.highlighted {
  background: var(--light);
}
.region-highlighted {
  display: flex;
  flex-direction: column;
}
.region-highlighted .block {
  padding: 1rem 0;
}
.region-highlighted .message {
  margin: 1rem 0;
}

/* content top and content bottom block region */
#content-top,
#content-bottom {
  display: block;
  width: 100%;
}
.region-content-top,
.region-content-bottom {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.region-content-top {
  margin-bottom: 1rem;
}
.region-content-bottom {
  margin-top: 1rem;
}
.region-content-top .block,
.region-content-bottom .block {
  padding: 1rem;
  background-color: var(--light);
  border-radius: 8px;
  box-shadow: var(--shadow);
}