@keyframes splide-loading{0%{transform:rotate(0)}to{transform:rotate(1turn)}}.splide__track--draggable{-webkit-touch-callout:none;-webkit-user-select:none;-ms-user-select:none;user-select:none}.splide__track--fade>.splide__list>.splide__slide{margin:0!important;opacity:0;z-index:0}.splide__track--fade>.splide__list>.splide__slide.is-active{opacity:1;z-index:1}.splide--rtl{direction:rtl}.splide__track--ttb>.splide__list{display:block}.splide__container{box-sizing:border-box;position:relative}.splide__list{backface-visibility:hidden;display:-ms-flexbox;display:flex;height:100%;margin:0!important;padding:0!important}.splide.is-initialized:not(.is-active) .splide__list{display:block}.splide__pagination{-ms-flex-align:center;align-items:center;display:-ms-flexbox;display:flex;-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-pack:center;justify-content:center;margin:0;pointer-events:none}.splide__pagination li{display:inline-block;line-height:1;list-style-type:none;margin:0;pointer-events:auto}.splide:not(.is-overflow) .splide__pagination{display:none}.splide__progress__bar{width:0}.splide{position:relative;visibility:hidden}.splide.is-initialized,.splide.is-rendered{visibility:visible}.splide__slide{backface-visibility:hidden;box-sizing:border-box;-ms-flex-negative:0;flex-shrink:0;list-style-type:none!important;margin:0;position:relative}.splide__slide img{vertical-align:bottom}.splide__spinner{animation:splide-loading 1s linear infinite;border:2px solid #999;border-left-color:transparent;border-radius:50%;bottom:0;contain:strict;display:inline-block;height:20px;left:0;margin:auto;position:absolute;right:0;top:0;width:20px}.splide__sr{clip:rect(0 0 0 0);border:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.splide__toggle.is-active .splide__toggle__play,.splide__toggle__pause{display:none}.splide__toggle.is-active .splide__toggle__pause{display:inline}.splide__track{overflow:hidden;position:relative;z-index:0}
.slider {
  background-image: url(../images/slider-bg.svg);
  background-repeat: no-repeat;
  background-size: contain;
}
.splide__track {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  align-items: center;
}
/* Arrow */
.splide__arrows {
  display: none;
}
.splide__pagination {
  gap: 0.5rem;
  padding: 0;
}
.splide__pagination__page {
  margin: 0;
  padding: 0;
  width: 14px;
  height: 2px;
}
.splide__pagination__page::before {
  content: none;
}
.splide__pagination .is-active {
  background-color: var(--color-secondary);
}
/* Slider content for user */
.splide__list {
  width: 100%;
}
.splide__slide {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100vh;
}
.slider-text,
.slider-img {
  flex: 0 0 50%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  height: 50vh;
  max-height: 50vh;
}
.slider-text {
  text-align: center;
}
.slider-img img {
  max-width: 100%;
  height: auto;
  max-height: 50vh;
  object-fit: scale-down;
}
/* Slider typography */
.splide__slide h1,
.splide__slide h2,
.splide__slide h3,
.splide__slide h4 {
  font-family: var(--font-text);
  font-weight: 400;
}
.splide__slide h1 {
  font-size: 2rem;
}
.splide__slide h2 {
  font-size: 2rem;
}
.splide__slide h3 {
  font-size: 1.4rem;
}

@media (min-width: 768px) {
  .splide__slide h1 {
    font-size: 3rem;
  }
  .splide__slide h2 {
    font-size: 2rem;
  }
  .splide__slide h3 {
    font-size: 1.6rem;
  }
}

@media (min-width: 1024px) {
  .splide__slide {
    flex-direction: row;
  }
  .slider-text,
  .slider-img {
    width: 50%;
    height: 100vh;
    max-height: 100vh;
  }
  .slider-text {
    text-align: left;
  }
  .slider-img img {
    max-height: 100vh;
  }
  .splide__arrows {
    display: block;
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
  }
  .splide__arrow {
    background-color: transparent;
    border: 1px solid #646971;
    border-radius: 0;
  }
  .splide__arrow:hover {
    background-color: rgba(0, 0, 0, 0.4);
  }
  .splide__arrow::before {
    content: none;
  }
  .splide__arrow--prev {
    margin-right: 4px;
    padding: 1rem 1rem 1rem 0;
  }
  .splide__arrow--next {
    margin-right: 4px;
    padding: 1rem 0 1rem 1rem;
  }
  .splide__arrow svg {
    fill: none;
    stroke: #646971;
    stroke-width: 2px;
  }
  .splide__arrow--prev svg {
    transform: scaleX(-1);
  }
  .splide__slide h1 {
    font-size: 4rem;
  }
  .splide__slide h2 {
    font-size: 3rem;
  }
  .splide__slide h3 {
    font-size: 2rem;
  }
}
