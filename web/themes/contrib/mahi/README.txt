
# Mahi
Mahi is a clean, elegant, professional, modern, multipurpose and fully responsive Drupal 9, 10 and 11 theme.


## THEME FEATURES
- Drupal 9.x, 10.x, 11.x compatible
- Fully responsive
- Clean & modern design
- HTML5 & CSS3
- Google Font (self hosted and CDN)
- Multi-level drop down main menu
- Google Material Font Icons
- FontAwesome Font Icons
- Bootstrap font icons
- Inbuilt slider for homepage.
- One column, two column, three column page layout.
- Intelligent dynamic columns
- Customizable theme setting
- and more..


## Theme page
https://drupar.com/theme/mahi


## Theme Demo
https://demo.drupar.com/mahi/


## Documentaion
https://drupar.com/doc/mahi


## Demo Content
https://drupar.com/doc/mahi/demo-site-content


## Custom Shortcodes
https://drupar.com/doc/mahi/custom-shortcodes


## Block region
https://drupar.com/doc/mahi/block-regions


## Requirements
Mahi theme does not require anything beyond Drupal 9 / 10 / 11 core to work.


## Installation
https://drupar.com/doc/mahi/how-install-mahi-theme


## Configuration
Navigate to: Administration >> Appearance >> Settings >> mahi


## maintainer
Current maintainer:
* Ravi Shekhar - https://www.drupal.org/u/ravis