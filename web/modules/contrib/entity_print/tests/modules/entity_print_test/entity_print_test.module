<?php

/**
 * @file
 * A module for testing Entity Print.
 */

use Drupal\Core\Extension\Extension;

/**
 * Implements hook_system_theme_info().
 */
function entity_print_test_system_theme_info() {
  $themes['entity_print_test_theme'] = \Drupal::service('extension.list.module')->getPath('entity_print_test') . '/themes/entity_print_test_theme/entity_print_test_theme.info';
  return $themes;
}

/**
 * Implements hook_entity_print_pdf_engine_alter().
 */
function entity_print_test_entity_print_pdf_engine_alter(&$definitions) {
  // Replace these with a class that always makes them not available so I don't
  // have to manually disable to run the tests locally where they're installed.
  $disabled_engines = ['dompdf', 'phpwkhtmltopdf'];
  foreach ($disabled_engines as $id) {
    if (isset($definitions[$id]['class'])) {
      $definitions[$id]['class'] = 'Drupal\entity_print_test\Plugin\EntityPrint\PrintEngine\NotAvailablePrintEngine';
    }
  }
}

/**
 * Implements hook_system_info_alter().
 */
function entity_print_test_system_info_alter(array &$info, Extension $file, $type) {
  if ($type === 'theme' && $file->getName() === 'entity_print_test_theme') {
    $info['entity_print']['node']['page'] = 'entity_print_test_theme/page';
  }
}
