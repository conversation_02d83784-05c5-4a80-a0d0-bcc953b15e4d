<?php

namespace Drupal\entity_print\Plugin;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheBackendInterface;
use <PERSON><PERSON><PERSON>\Core\Extension\ModuleHandlerInterface;
use <PERSON><PERSON><PERSON>\Core\Extension\ThemeHandlerInterface;
use <PERSON><PERSON>al\Core\Plugin\DefaultPluginManager;
use <PERSON><PERSON><PERSON>\Core\Plugin\Discovery\ContainerDerivativeDiscoveryDecorator;
use Dr<PERSON>al\Core\Plugin\Discovery\YamlDiscovery;

/**
 * Export type manager.
 */
class ExportTypeManager extends DefaultPluginManager implements ExportTypeManagerInterface {

  /**
   * The module handler.
   *
   * @var \Drupal\Core\Extension\ModuleHandlerInterface
   */
  protected $moduleHandler;

  /**
   * The theme handler.
   *
   * @var \Drupal\Core\Extension\ThemeHandlerInterface
   */
  protected $themeHandler;

  /**
   * Provides default values for plugins.
   *
   * @var array
   */
  protected $defaults = [
    'class' => '\Drupal\entity_print\Plugin\EntityPrint\ExportType\DefaultExportType',
  ];

  /**
   * Constructs a new export type manager.
   *
   * @param \Drupal\Core\Cache\CacheBackendInterface $cache_backend
   *   The cache backend.
   * @param \Drupal\Core\Extension\ModuleHandlerInterface $module_handler
   *   The module handler.
   * @param \Drupal\Core\Extension\ThemeHandlerInterface $theme_handler
   *   The theme handler.
   */
  public function __construct(CacheBackendInterface $cache_backend, ModuleHandlerInterface $module_handler, ThemeHandlerInterface $theme_handler) {
    $this->alterInfo('entity_print_export_types');
    $this->setCacheBackend($cache_backend, 'entity_print_export_types', ['entity_print_export_types']);
    $this->moduleHandler = $module_handler;
    $this->themeHandler = $theme_handler;
  }

  /**
   * {@inheritdoc}
   */
  protected function getDiscovery() {
    if (!isset($this->discovery)) {
      $this->discovery = new YamlDiscovery('entity_print_export_types', $this->moduleHandler->getModuleDirectories() + $this->themeHandler->getThemeDirectories());
      $this->discovery = new ContainerDerivativeDiscoveryDecorator($this->discovery);
    }
    return $this->discovery;
  }

  /**
   * {@inheritdoc}
   */
  public function getFormOptions() {
    $export_types = [];
    $definitions = $this->getDefinitions();
    foreach ($definitions as $export_type => $definition) {
      $export_types[$export_type] = $definition['label'];
    }
    return $export_types;
  }

}
