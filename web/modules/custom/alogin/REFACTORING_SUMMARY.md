# MFA Route Subscriber Refactoring Summary

## Overview

This refactoring implements a cleaner approach to handling MFA-protected API login requests by using a route subscriber pattern instead of handling it in an event subscriber. This follows Drupal best practices and provides better separation of concerns.

## Changes Made

### 1. Created Route Subscriber (`src/Routing/AloginRouteSubscriber.php`)

- Implements `RouteSubscriberBase` and `EventSubscriberInterface`
- Overrides the `user.login.http` route to point to our custom controller
- Uses priority `-100` to ensure it runs after other route alterations

### 2. Created MFA Login Controller (`src/Controller/MfaLoginController.php`)

- Extends `UserAuthenticationController` to inherit base login functionality
- Implements comprehensive MFA validation logic for API requests
- Handles different scenarios:
  - MFA not enabled globally → proceed with normal login
  - User doesn't have MFA configured → check enforcement rules
  - User has MFA configured → require and validate MFA token
- Provides detailed error messages for different failure scenarios

### 3. Enhanced Authenticator Service (`src/AuthenticatorService.php`)

- Added `verifyCode($code, $uid)` method for validating MFA codes for specific users
- Modified `exists($uid = NULL)` method to accept optional user ID parameter
- Maintains backward compatibility with existing code

### 4. Updated Services Configuration (`alogin.services.yml`)

- Added route subscriber service registration
- Maintains existing event subscriber for web-based MFA redirects

### 5. Cleaned Up Event Subscriber (`src/EventSubscriber/MfaRedirectSubscriber.php`)

- Removed API request handling logic (now handled by route subscriber)
- Simplified to focus only on web-based MFA redirects
- Removed unused imports and methods

## Benefits of This Approach

### 1. **Better Separation of Concerns**
- Route subscriber handles route alterations
- Controller handles API login logic
- Event subscriber focuses on web redirects

### 2. **Cleaner Code Architecture**
- Follows Drupal's recommended patterns
- Easier to test and maintain
- More predictable execution flow

### 3. **Enhanced Security**
- Comprehensive validation logic
- Proper error handling
- Detailed logging capabilities

### 4. **Improved Maintainability**
- Clear separation between API and web handling
- Easier to extend or modify individual components
- Better code organization

## API Usage Examples

### Successful Login (No MFA Required)
```bash
curl -X POST http://example.com/user/login \
  -H "Content-Type: application/json" \
  -d '{"name": "username", "pass": "password"}'
```

### MFA-Protected Login
```bash
curl -X POST http://example.com/user/login \
  -H "Content-Type: application/json" \
  -d '{"name": "username", "pass": "password", "mfa_token": "123456"}'
```

### Error Responses

#### Missing MFA Token
```json
{
  "error": "Two-factor authentication token is required.",
  "status": 403
}
```

#### Invalid MFA Token
```json
{
  "error": "Invalid two-factor authentication token.",
  "status": 403
}
```

#### MFA Not Configured (When Enforced)
```json
{
  "error": "Two-factor authentication is required but not configured for this user.",
  "status": 403
}
```

## Configuration Considerations

### 1. **MFA Enforcement Settings**
- `redirect`: Global MFA enablement
- `allow_enable_disable`: Whether users can disable MFA
- `redirect_message`: Message shown to users

### 2. **Permission-Based Bypass**
- Users with `alogin bypass enforced redirect` permission can bypass MFA requirements
- Useful for administrative accounts or emergency access

### 3. **Content Type Detection**
- Automatically detects API requests based on Content-Type headers
- Supports JSON and XML formats
- Falls back to web-based handling for browser requests

## Testing Recommendations

### 1. **Unit Tests**
- Test MFA validation logic
- Test different user scenarios
- Test error handling

### 2. **Integration Tests**
- Test API endpoints with various payloads
- Test route subscriber functionality
- Test interaction between components

### 3. **Security Tests**
- Test bypass attempts
- Test token validation
- Test rate limiting (if implemented)

## Future Enhancements

### 1. **Rate Limiting**
- Implement rate limiting for MFA attempts
- Use Drupal's flood control service

### 2. **Audit Logging**
- Enhanced logging for security events
- Integration with external logging systems

### 3. **Multiple MFA Methods**
- Support for SMS, email, or hardware tokens
- Backup codes for recovery

### 4. **API Documentation**
- OpenAPI/Swagger documentation
- Client SDK examples

## Migration Notes

- Existing web-based MFA functionality remains unchanged
- API clients need to include `mfa_token` in login requests
- No database schema changes required
- Backward compatible with existing configurations

## Dependencies

- `serialization` module (for API serialization)
- `rest` module (for REST API functionality)
- Existing `alogin` module dependencies
