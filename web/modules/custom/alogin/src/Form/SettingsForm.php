<?php

namespace Drupal\alogin\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON><PERSON>al\Core\Ajax\ReplaceCommand;
use <PERSON><PERSON>al\Core\Ajax\RedirectCommand;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\Core\Render\Markup;
use Drupal\Component\Render\FormattableMarkup;

/**
 * Class SettingsForm.
 */
class SettingsForm extends FormBase
{

    /**
     * Drupal\Core\StringTranslation\TranslationManager definition.
     *
     * @var \Drupal\Core\StringTranslation\TranslationManager
     */
    protected $stringTranslation;

    /**
     * Drupal\Core\Session\AccountProxyInterface definition.
     *
     * @var \Drupal\Core\Session\AccountProxyInterface
     */
    protected $currentUser;

    /**
     * Drupal\Core\Messenger\MessengerInterface definition.
     *
     * @var \Drupal\Core\Messenger\MessengerInterface
     */
    protected $messenger;

    /**
     * Drupal\Core\Database\Driver\mysql\Connection definition.
     *
     * @var \Drupal\Core\Database\Driver\mysql\Connection
     */
    protected $database;

    /**
     * Drupal\alogin\AuthenticatorService definition.
     *
     * @var \Drupal\alogin\AuthenticatorService
     */
    protected $aloginAuthenticator;

    /**
     * Drupal\Core\TempStore\PrivateTempStoreFactory definition.
     *
     * @var \Drupal\Core\TempStore\PrivateTempStoreFactory
     */
    protected $tempstorePrivate;

    /**
     * {@inheritdoc}
     */
    public static function create(ContainerInterface $container)
    {
        $instance = parent::create($container);
        $instance->stringTranslation = $container->get('string_translation');
        $instance->currentUser = $container->get('current_user');
        $instance->messenger = $container->get('messenger');
        $instance->database = $container->get('database');
        $instance->tempstorePrivate = $container->get('tempstore.private');
        $instance->aloginAuthenticator = $container->get('alogin.authenticator');
        return $instance;
    }

    /**
     * {@inheritdoc}
     */
    public function getFormId()
    {
        return 'settings_form';
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(array $form, FormStateInterface $form_state)
    {
        $authenticator = $this->aloginAuthenticator;
        $config = $this->config('alogin.config');
        if ($config->get('allow_enable_disable')) {
          $form['enable_2fa'] = [
            '#type' => 'checkbox',
            '#title' => $this->t('Enable 2FA'),
            '#default_value' => self::getDefaults(),
            '#weight' => 0
          ];
        }
        $form['2fa_fieldset'] = [
          '#type' => 'details',
          '#title' => t('2FA Settings'),
          '#collapsable' => true,
          '#open'  => true,
          '#states' => [
            'visible' => [
              'input[name="enable_2fa"]' => ['checked' => true]
            ]
          ]
        ];
        $qr_code_value = $authenticator->getQr();
        $qr_code = str_contains($qr_code_value, 'data:image/png') ? new FormattableMarkup('<img src="data::src" alt="2FA QR Code" />', [':src' => $qr_code_value]) : Markup::create($qr_code_value);
        $form['2fa_fieldset']['qr'] = [
          '#type' => 'markup',
          '#markup' => $qr_code,
          '#prefix' => '<div class="qr-frame">',
          '#suffix' => '</div>'
        ];
        $form['2fa_fieldset']['code'] = [
          '#type' => 'textfield',
          '#title' => t('Code'),
          '#description' => t('Enter the code generated by the authenticator app.')
        ];
        $form['submit'] = [
          '#type' => 'submit',
          '#value' => $this->t('Submit'),
        ];
        return $form;
    }

    /**
     * {@inheritdoc}
     */
    public function validateForm(array &$form, FormStateInterface $form_state)
    {
        if ($form_state->getValue('enable_2fa') && $this->aloginAuthenticator->check($form_state->getValue('code')) == false) {
            $form_state->setErrorByName('code', $this->t('Invalid or expired code.'));
        }
        parent::validateForm($form, $form_state);
    }

    /**
     * {@inheritdoc}
     */
    public function submitForm(array &$form, FormStateInterface $form_state)
    {
        $config = $this->config('alogin.config');
        $enable = $config->get('allow_enable_disable') ? $form_state->getValue('enable_2fa') : 1;
        $this->aloginAuthenticator->store($enable);
        if ($enable) {
            $this->messenger->addMessage($this->t('2FA via authenticator has been enabled on your account.'));
        } else {
            $this->messenger->addMessage($this->t('2FA via authenticator has been disabled on your account.'));
        }
        $this->tempstorePrivate->get('alogin')->delete('secret');
    }

    private function getDefaults()
    {
        $default = $this->database->select('alogin_user_settings', 'a')
            ->fields('a', ['enabled'])
            ->condition('uid', $this->currentUser->id(), '=')
            ->execute()
            ->fetchAssoc();
        return $default ? $default['enabled'] : false;
    }

}
