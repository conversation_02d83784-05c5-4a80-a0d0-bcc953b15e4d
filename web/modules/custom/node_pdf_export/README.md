# Node PDF Export

A Drupal 10 module that allows exporting nodes to PDF format with download functionality.

## Features

- Export any node to PDF format
- PDF preview functionality
- Configurable PDF generation methods
- Support for wkhtmltopdf binary (when available)
- HTML fallback when PDF libraries are not available
- Customizable styling and content options
- Permission-based access control
- Integration with node display and links

## Installation

1. Place the module in `web/modules/custom/node_pdf_export`
2. Enable the module: `drush en node_pdf_export`
3. Configure permissions at `/admin/people/permissions`
4. Configure module settings at `/admin/config/content/node-pdf-export`

## Configuration

### PDF Generation Methods

1. **HTML Fallback**: Generates an HTML file that can be manually converted to PDF
2. **wkhtmltopdf**: Uses the wkhtmltopdf binary for direct PDF generation

### Required Permissions

- **Export nodes to PDF**: Allows users to export nodes to PDF
- **Administer Node PDF Export**: Allows configuration of module settings

### Content Settings

- Choose which content types support PDF export
- Select view mode for PDF rendering
- Include/exclude comments and node links
- Custom CSS styling

## Usage

### Configuring the PDF Export Button

1. **Navigate to Manage Display**: Go to Structure » Content types » [Your Content Type] » Manage display
2. **Enable PDF Export Button**: Find "PDF Export Button" in the disabled fields section and drag it to the desired region
3. **Configure Button Settings**: Click the gear icon next to "PDF Export Button" to configure:
   - **Button Text**: Customize the text displayed on the button (e.g., "Download PDF", "Get PDF", etc.)
   - **Button Style**: Choose between "Button" (styled button) or "Link" (simple text link)
   - **Show Icon**: Toggle the PDF icon (📄) next to the button text
4. **Save**: Click "Save" to apply your configuration

### For End Users

1. Navigate to any node page where the PDF Export Button is enabled
2. Click the configured PDF export button to download the node as PDF

### For Administrators

1. Go to `/admin/config/content/node-pdf-export`
2. Configure global PDF settings (orientation, format, etc.)
3. Set up content and styling options
4. Enable for specific content types

## Technical Details

### PDF Generation

The module supports multiple PDF generation methods:

1. **wkhtmltopdf**: Requires the wkhtmltopdf binary to be installed on the server
2. **HTML Fallback**: Generates a styled HTML file that can be converted to PDF

### File Handling

- Generated files are stored in the temporary directory
- Files are served with appropriate headers for download
- Automatic cleanup of temporary files

### Theming

The module provides:
- Custom CSS for PDF styling
- Twig templates for PDF export actions
- Configurable view modes for PDF content

## Dependencies

- Drupal Core 10.x
- Node module (core)
- System module (core)

## Optional Dependencies

- wkhtmltopdf binary (for direct PDF generation)

## Troubleshooting

### PDF Generation Issues

1. **wkhtmltopdf not found**: 
   - Install wkhtmltopdf: `sudo apt-get install wkhtmltopdf`
   - Or use HTML fallback method

2. **Permission denied**:
   - Check file permissions in temporary directory
   - Verify wkhtmltopdf binary is executable

3. **HTML fallback only**:
   - This is normal when PDF libraries are not available
   - HTML files can be manually converted to PDF

### Performance

- Large nodes may take longer to process
- Consider using caching for frequently exported content
- Monitor temporary directory disk usage

## Development

### Extending the Module

The module is designed to be extensible:

1. **Custom PDF generators**: Implement additional PDF generation methods
2. **Styling**: Add custom CSS through the configuration form
3. **Content filtering**: Modify the PDF service to filter content
4. **Integration**: Use the service in other modules

### API Usage

```php
// Get the PDF generator service
$pdf_generator = \Drupal::service('node_pdf_export.pdf_generator');

// Generate PDF for a node
$node = \Drupal\node\Entity\Node::load($nid);
$file_path = $pdf_generator->generatePdf($node);
```

## License

GPL-2.0-or-later

## Maintainers

- Custom development module
