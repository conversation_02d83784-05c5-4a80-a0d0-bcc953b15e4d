<?php

/**
 * @file
 * Node PDF Export module.
 */

use Dr<PERSON>al\Core\Entity\EntityInterface;
use Drupal\Core\Entity\Display\EntityViewDisplayInterface;
use Drupal\Core\Url;
use Dr<PERSON>al\node\NodeInterface;

/**
 * Implements hook_entity_extra_field_info().
 */
function node_pdf_export_entity_extra_field_info() {
  $extra = [];

  $config = \Drupal::config('node_pdf_export.settings');
  $enabled_types = $config->get('enabled_content_types') ?: [];

  // Get all node types if none are specifically enabled.
  $node_types = \Drupal::entityTypeManager()->getStorage('node_type')->loadMultiple();

  foreach ($node_types as $bundle) {
    // Only add to enabled content types, or all if none specified.
    if (empty($enabled_types) || in_array($bundle->id(), $enabled_types)) {
      $extra['node'][$bundle->id()]['display']['node_pdf_export_button'] = [
        'label' => t('PDF Export Button'),
        'description' => t('A configurable button to download the node as PDF'),
        'weight' => 100,
        'visible' => FALSE,
      ];
    }
  }

  return $extra;
}

/**
 * Implements hook_entity_view().
 */
function node_pdf_export_entity_view(array &$build, EntityInterface $entity, EntityViewDisplayInterface $display, $view_mode) {
  if ($entity instanceof NodeInterface) {
    $component = $display->getComponent('node_pdf_export_button');

    if ($component) {
      /** @var \Drupal\node_pdf_export\Service\PdfExportButtonManager $button_manager */
      $button_manager = \Drupal::service('node_pdf_export.button_manager');
      $button_element = $button_manager->buildButton($entity, $display);

      if (!empty($button_element)) {
        $build['node_pdf_export_button'] = $button_element;
      }
    }
  }
}

/**
 * Implements hook_node_links_alter().
 */
function node_pdf_export_node_links_alter(array &$links, NodeInterface $entity, array &$context) {
  $current_user = \Drupal::currentUser();

  if ($current_user->hasPermission('export nodes to pdf') && $entity->access('view')) {
    $export_url = Url::fromRoute('node_pdf_export.export', ['node' => $entity->id()]);
    $preview_url = Url::fromRoute('node_pdf_export.preview', ['node' => $entity->id()]);

    $links['pdf_export'] = [
      'title' => t('Export PDF'),
      'url' => $export_url,
      'attributes' => [
        'class' => ['pdf-export-link'],
        'target' => '_blank',
      ],
    ];

    $links['pdf_preview'] = [
      'title' => t('PDF Preview'),
      'url' => $preview_url,
      'attributes' => [
        'class' => ['pdf-preview-link'],
      ],
    ];
  }
}

/**
 * Implements hook_form_FORM_ID_alter() for entity_view_display_edit_form.
 */
function node_pdf_export_form_entity_view_display_edit_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  /** @var \Drupal\Core\Entity\Display\EntityViewDisplayInterface $entity */
  $entity = $form_state->getFormObject()->getEntity();

  if ($entity->getTargetEntityTypeId() === 'node') {
    // Check if our PDF export button field is present.
    if (isset($form['fields']['node_pdf_export_button'])) {
      $component = $entity->getComponent('node_pdf_export_button');
      $settings = $component['settings'] ?? \Drupal\node_pdf_export\Service\PdfExportButtonManager::getDefaultSettings();

      // Add custom submit handler to save our settings.
      $form['#submit'][] = 'node_pdf_export_entity_view_display_edit_form_submit';

      // Add settings form elements.
      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form'] = [
        '#type' => 'container',
        '#attributes' => ['class' => ['field-plugin-settings-edit-form']],
        '#parents' => ['fields', 'node_pdf_export_button', 'settings_edit_form'],
      ];

      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['label'] = [
        '#markup' => t('PDF Export Button Settings'),
      ];

      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['settings'] = [
        '#type' => 'fieldset',
        '#title' => t('Button Configuration'),
      ];

      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['settings']['button_text'] = [
        '#type' => 'textfield',
        '#title' => t('Button Text'),
        '#default_value' => $settings['button_text'],
        '#description' => t('The text to display on the PDF export button.'),
        '#required' => TRUE,
      ];

      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['settings']['button_style'] = [
        '#type' => 'select',
        '#title' => t('Button Style'),
        '#default_value' => $settings['button_style'],
        '#options' => [
          'button' => t('Button'),
          'link' => t('Link'),
        ],
        '#description' => t('Choose how to display the PDF export element.'),
      ];

      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['settings']['show_icon'] = [
        '#type' => 'checkbox',
        '#title' => t('Show Icon'),
        '#default_value' => $settings['show_icon'],
        '#description' => t('Show a PDF icon next to the button text.'),
      ];

      // Add actions.
      $form['fields']['node_pdf_export_button']['plugin']['settings_edit_form']['actions'] = [
        '#type' => 'actions',
        'save_settings' => [
          '#type' => 'submit',
          '#button_type' => 'primary',
          '#name' => 'node_pdf_export_button_plugin_settings_update',
          '#value' => t('Update'),
          '#op' => 'update',
          '#submit' => ['::multistepSubmit'],
          '#ajax' => [
            'callback' => '::multistepAjax',
            'wrapper' => 'field-display-overview-wrapper',
            'effect' => 'fade',
          ],
          '#field_name' => 'node_pdf_export_button',
        ],
        'cancel_settings' => [
          '#type' => 'submit',
          '#name' => 'node_pdf_export_button_plugin_settings_cancel',
          '#value' => t('Cancel'),
          '#op' => 'cancel',
          '#submit' => ['::multistepSubmit'],
          '#ajax' => [
            'callback' => '::multistepAjax',
            'wrapper' => 'field-display-overview-wrapper',
            'effect' => 'fade',
          ],
          '#field_name' => 'node_pdf_export_button',
          '#limit_validation_errors' => [['fields', 'node_pdf_export_button', 'type']],
        ],
      ];
    }
  }
}

/**
 * Custom submit handler for entity view display edit form.
 */
function node_pdf_export_entity_view_display_edit_form_submit($form, \Drupal\Core\Form\FormStateInterface $form_state) {
  /** @var \Drupal\Core\Entity\Display\EntityViewDisplayInterface $entity */
  $entity = $form_state->getFormObject()->getEntity();

  if ($entity->getTargetEntityTypeId() === 'node') {
    $form_values = $form_state->getValues();

    if (isset($form_values['fields']['node_pdf_export_button']['settings_edit_form']['settings'])) {
      $settings = $form_values['fields']['node_pdf_export_button']['settings_edit_form']['settings'];
      $component = $entity->getComponent('node_pdf_export_button');

      if ($component) {
        $component['settings'] = $settings;
        $entity->setComponent('node_pdf_export_button', $component);
      }
    }
  }
}

/**
 * Implements hook_theme().
 */
function node_pdf_export_theme() {
  return [
    'node_pdf_export_actions' => [
      'variables' => [
        'node' => NULL,
        'export_url' => NULL,
        'preview_url' => NULL,
      ],
      'template' => 'node-pdf-export-actions',
    ],
  ];
}
