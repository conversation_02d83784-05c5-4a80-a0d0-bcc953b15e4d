<?php

/**
 * Test script to verify MFA vulnerability is fixed
 */

// Simulate the vulnerable curl request
$url = 'http://localhost/user/login?_format=json';
$data = json_encode([
    'name' => 'testuser',
    'pass' => 'testpassword123'
]);

$headers = [
    'Content-Type: application/json',
    'Accept: application/json'
];

echo "Testing MFA vulnerability fix...\n";
echo "URL: $url\n";
echo "Data: $data\n";
echo "Headers: " . implode(', ', $headers) . "\n\n";

// Initialize cURL
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_VERBOSE, true);

// Execute request
$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "HTTP Status Code: $http_code\n";
if ($error) {
    echo "cURL Error: $error\n";
}

echo "Response:\n";
echo $response . "\n";

// Analyze response
if ($http_code === 200) {
    echo "\n❌ VULNERABILITY STILL EXISTS: Login succeeded without MFA!\n";
} elseif ($http_code === 403) {
    echo "\n✅ VULNERABILITY FIXED: Login properly blocked by MFA requirement!\n";
} else {
    echo "\n⚠️  UNEXPECTED RESPONSE: HTTP $http_code\n";
}
